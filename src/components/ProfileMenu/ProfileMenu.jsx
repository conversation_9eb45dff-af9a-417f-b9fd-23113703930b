import React from "react";
import {
  Box,
  IconButton,
  Typography,
  Menu,
  MenuItem,
  CircularProgress,
  Popover,
  Divider,
} from "@mui/material";
import { useNavigate } from "react-router-dom";
import siteConstant from "../../constants/siteConstants";
import URL from "../../constants/urls";
import Notification from "../../assets/images/notification.svg";

const ProfileMenu = ({
  selectedUser,
  loadingUsers,
  allUsersData,
  dropdownAnchorEl,
  notifyAnchorEl,
  profileAnchorEl,
  imageLoading,
  imageError,
  handleDropdownOpen,
  handleDropdownClose,
  onUserChange,
  HandleNotify,
  handleNotifyClose,
  handleProfileOpen,
  handleProfileClose,
  setImageLoading,
  setImageError,
  setOpenDelete,
  openDelete,
  logout,
  localesData,
}) => {
  const navigate = useNavigate();

  return (
    <Box className="flex items-center">
      <Box
        className="mr-2 sm:mr-3 md:mr-4"
        sx={{
          width: { xs: "140px", sm: "160px", md: "180px", lg: "200px" },
          flexShrink: 0,
          display: "flex",
          justifyContent: "flex-end",
        }}
      >
        <IconButton
          onClick={handleDropdownOpen}
          sx={{
            width: "100%",
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            borderRadius: "10px",
            padding: "10px 14px",
            backgroundColor: "#f9f9f9",
            transition: "all 0.2s ease-in-out",
            "&:hover": {
              backgroundColor: "#f1f1f1",
              boxShadow: "0 1px 5px rgba(0, 0, 0, 0.1)",
            },
          }}
        >
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              gap: 1,
              flex: 1,
              minWidth: 0,
            }}
          >
            <div className="flex justify-center items-center gap-2">
              <Box
                sx={{
                  width: {
                    xs: "20px",
                    sm: "24px",
                    md: "28px",
                    lg: "32px",
                  },
                  height: {
                    xs: "20px",
                    sm: "24px",
                    md: "28px",
                    lg: "32px",
                  },
                  flexShrink: 0,
                }}
              >
                {selectedUser ? (
                  <img
                    src={
                      selectedUser.profile_image ||
                      siteConstant.SOCIAL_ICONS.FLOWKARLOGO
                    }
                    onError={(e) => {
                      e.target.onerror = null;
                      e.target.src = siteConstant.SOCIAL_ICONS.FLOWKARLOGO;
                    }}
                    alt={selectedUser.name}
                    style={{
                      width: "100%",
                      height: "100%",
                      borderRadius: "50%",
                      objectFit: "cover",
                    }}
                  />
                ) : (
                  <Box
                    sx={{
                      width: "100%",
                      height: "100%",
                      borderRadius: "50%",
                      backgroundColor: "#f0f0f0",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  >
                    <Typography sx={{ fontSize: "12px", color: "#999" }}>
                      U
                    </Typography>
                  </Box>
                )}
              </Box>

              <Box
                sx={{
                  display: { xs: "none", sm: "block" },
                  minWidth: 0,
                  flex: 1,
                }}
              >
                <Typography
                  sx={{
                    fontFamily: "Ubuntu",
                    fontSize: { xs: "12px", sm: "13px", md: "14px" },
                    fontWeight: 500,
                    color: "#000000",
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    whiteSpace: "nowrap",
                    lineHeight: 1.2,
                  }}
                >
                  {selectedUser?.name || "Select User"}
                </Typography>
              </Box>
            </div>
          </Box>

          <img
            src={siteConstant.SOCIAL_ICONS.DROPDOWN_ARROW}
            alt="DropDown"
            className="h-3 w-3 sm:h-4 sm:w-4"
            style={{
              transform: dropdownAnchorEl ? "rotate(180deg)" : "rotate(0deg)",
              transition: "transform 0.2s ease-in-out",
            }}
          />
        </IconButton>

        <Menu
          anchorEl={dropdownAnchorEl}
          open={Boolean(dropdownAnchorEl)}
          onClose={handleDropdownClose}
          sx={{
            "& .MuiPaper-root": {
              minWidth: "220px",
              maxHeight: "300px",
              overflowY: "auto",
              boxShadow: "0px 8px 20px rgba(0, 0, 0, 0.08)",
              borderRadius: "10px",
              padding: "4px 0",
            },
          }}
        >
          {loadingUsers ? (
            <MenuItem disabled sx={{ py: 1 }}>
              <CircularProgress size={20} sx={{ mr: 2 }} />
              Loading users...
            </MenuItem>
          ) : allUsersData.length > 0 ? (
            allUsersData.map((user) => {
              return (
                <MenuItem
                  key={user.id}
                  onClick={() => {
                    onUserChange(user);
                  }}
                  sx={{
                    alignItems: "flex-start",
                    display: "flex",
                    gap: 2,
                    px: 2,
                    py: 2,
                    borderBottom: "1px solid #eee",
                    "&:last-of-type": {
                      borderBottom: "none",
                    },
                    "&:hover": {
                      backgroundColor: "#f5f5f5",
                    },
                  }}
                >
                  <Box
                    sx={{
                      width: 48,
                      height: 48,
                      borderRadius: "50%",
                      position: "relative",
                      flexShrink: 0,
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  >
                    {imageLoading && (
                      <CircularProgress
                        size={24}
                        sx={{
                          position: "absolute",
                          zIndex: 1,
                        }}
                      />
                    )}

                    {!imageError && user.profile_image ? (
                      <img
                        src={user.profile_image}
                        alt={user.name}
                        onLoad={() => {
                          setImageLoading(false);
                          setImageError(false);
                        }}
                        onError={() => {
                          setImageLoading(false);
                          setImageError(true);
                        }}
                        style={{
                          width: 48,
                          height: 48,
                          borderRadius: "100%",
                          objectFit: "cover",
                          opacity: imageLoading ? 0 : 1,
                          transition: "opacity 0.3s ease-in-out",
                        }}
                      />
                    ) : (
                      <Box
                        sx={{
                          width: 48,
                          height: 48,
                          borderRadius: "50%",
                          backgroundColor: "#f0f0f0",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                        }}
                      >
                        <Typography sx={{ fontSize: "12px", color: "#999" }}>
                          {user.name.charAt(0).toUpperCase()}
                        </Typography>
                      </Box>
                    )}
                  </Box>

                  <Box sx={{ minWidth: 0, flex: 1 }}>
                    <Typography
                      sx={{
                        fontFamily: "Ubuntu",
                        fontSize: "18px",
                        fontWeight: 500,
                        color: "#000000",
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        whiteSpace: "nowrap",
                      }}
                    >
                      {user.name}
                    </Typography>
                    <Typography
                      sx={{
                        fontFamily: "Ubuntu",
                        fontSize: "16px",
                        color: "#A9ABAD",
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        whiteSpace: "nowrap",
                      }}
                    >
                      @{user.username || user.email || `ID: ${user.user_id}`}
                    </Typography>
                    <Box sx={{ display: "flex", gap: 1, mt: 1 }}>
                      {Object.entries(user)
                        .filter(([key, value]) => {
                          // Filter for platform keys that are true
                          const platformKeys = [
                            "Facebook",
                            "Instagram",
                            "x",
                            "YouTube",
                            "LinkedIn",
                            "Pinterest",
                            "tiktok",
                            "threads",
                            "Dailymotion",
                            "Twitter",
                            "tumblr",
                            "Vimeo",
                            "reddit",
                            "telegram",
                            "mastodon",
                          ];
                          return platformKeys.includes(key) && value === true;
                        })
                        .map(([platform]) => {
                          // Transform platform name to match icon key
                          let iconKey;
                          if (platform === "x") {
                            iconKey = "X_ICON";
                          } else {
                            iconKey = `${platform.toUpperCase()}_ICON`;
                          }

                          const iconSrc = siteConstant.SOCIAL_ICONS[iconKey];
                          if (!iconSrc) return null; // Handle missing icons

                          return (
                            <img
                              key={platform}
                              src={iconSrc}
                              alt={platform}
                              style={{
                                width: 26,
                                height: 26,
                                borderRadius: "100%",
                                marginLeft: "-12px",
                              }}
                            />
                          );
                        })}
                    </Box>
                  </Box>
                </MenuItem>
              );
            })
          ) : (
            <MenuItem disabled sx={{ py: 1, px: 2 }}>
              <Typography sx={{ fontSize: "0.875rem", color: "#999" }}>
                No users found
              </Typography>
            </MenuItem>
          )}
        </Menu>
      </Box>

      <IconButton
        onClick={HandleNotify}
        className="flex text-sm rounded-full focus:ring-gray-300"
        sx={{ flexShrink: 0 }}
      >
        <img
          src={Notification}
          alt="DropDown"
          className="h-6 w-6 sm:h-7 sm:w-7 md:h-8 md:w-8 rounded-2xl p-1"
        />
      </IconButton>
      <Popover
        className="ml-[-10px] mt-[13px]"
        id={Boolean(notifyAnchorEl) ? "notification-popover" : undefined}
        open={Boolean(notifyAnchorEl)}
        anchorEl={notifyAnchorEl}
        onClose={handleNotifyClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
        PaperProps={{
          sx: {
            mt: 1,
            borderRadius: 2,
            boxShadow: "0px 8px 20px rgba(0, 0, 0, 0.08)",
            minWidth: "300px",
            maxHeight: "400px",
            overflowY: "auto",
          },
        }}
      >
        <div className="p-4">
          <Typography
            variant="h6"
            sx={{
              fontFamily: "Ubuntu",
              fontWeight: 600,
              color: "#333",
              mb: 2,
            }}
          >
            Notifications
          </Typography>
          <Typography
            sx={{
              fontFamily: "Ubuntu",
              color: "#666",
              fontSize: "14px",
            }}
          >
            No new notifications
          </Typography>
        </div>
      </Popover>

      <IconButton
        onClick={handleProfileOpen}
        className="flex text-sm rounded-full focus:ring-gray-300"
        sx={{ flexShrink: 0 }}
      >
        <img
          src={siteConstant.SOCIAL_ICONS.PROFILE_MENU}
          alt="Profile Menu"
          className="h-6 w-6 sm:h-7 sm:w-7 md:h-8 md:w-8 rounded-2xl p-1"
        />
      </IconButton>
      <Popover
        className="ml-[-10px] mt-[13px]"
        id={Boolean(profileAnchorEl) ? "profile-popover" : undefined}
        open={Boolean(profileAnchorEl)}
        anchorEl={profileAnchorEl}
        onClose={handleProfileClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
        PaperProps={{
          sx: {
            mt: 1,
            borderRadius: 2,
            boxShadow: "0px 8px 20px rgba(0, 0, 0, 0.08)",
            minWidth: "200px",
          },
        }}
      >
        <div className="p-2">
          <MenuItem
            sx={{
              "&:hover": {
                backgroundColor: "#F9FAFB",
                color: "#674941",
              },
            }}
            onClick={() => {
              navigate("/plateforms");
              handleProfileClose();
            }}
          >
            <IconButton edge="start" color="inherit" aria-label="delete">
              <img
                className="h-4 w-4 ms-1 text-Red"
                src={siteConstant.SOCIAL_ICONS.BRAND_MANAGEMENT}
                alt="Delete Account"
              />
            </IconButton>
            <p className="text-sm font-semibold font-Ubuntu ms-[2px] text-Red">
              Plateforms
            </p>
          </MenuItem>
          <Divider />

          <MenuItem
            sx={{
              "&:hover": {
                backgroundColor: "#F9FAFB",
                color: "#674941",
              },
            }}
            onClick={() => {
              navigate("/feedback");
              handleProfileClose();
            }}
          >
            <IconButton edge="start" color="inherit" aria-label="feedback">
              <img
                className="h-4 w-4 ms-1 text-Red"
                src={siteConstant.SOCIAL_ICONS.FEEDBACK}
                alt="Feedback"
              />
            </IconButton>
            <p className="text-sm font-semibold font-Ubuntu ms-[2px] text-Red">
              {localesData?.USER_WEB?.FEEDBACK}
            </p>
          </MenuItem>
          <Divider />

          <MenuItem
            sx={{
              "&:hover": {
                backgroundColor: "#F9FAFB",
                color: "#674941",
              },
            }}
            onClick={() => {
              setOpenDelete(!openDelete);
              handleProfileClose();
            }}
          >
            <IconButton edge="start" color="inherit" aria-label="delete">
              <img
                className="h-4 w-4 ms-1 text-Red"
                src={siteConstant.SOCIAL_ICONS.DELETE}
                alt="Delete Account"
              />
            </IconButton>
            <p className="text-sm font-semibold font-Ubuntu ms-[2px] text-Red">
              {localesData?.USER_WEB?.DELETE_ACCOUNT}
            </p>
          </MenuItem>
          <Divider />
          <MenuItem
            sx={{
              "&:hover": {
                backgroundColor: "#F9FAFB",
              },
            }}
            onClick={logout}
          >
            <IconButton edge="start" color="inherit" aria-label="sign-out">
              <img
                className="h-4 w-4 ms-1"
                src={siteConstant.SOCIAL_ICONS.SIGN_OUT}
                alt="Sign Out"
              />
            </IconButton>
            <p className="text-sm font-semibold font-Ubuntu ms-[2px] text-Red">
              {localesData?.USER_WEB?.SIGN_OUT}
            </p>
          </MenuItem>
        </div>
      </Popover>
    </Box>
  );
};

export default ProfileMenu;
